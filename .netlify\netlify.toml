functionsDirectory = "F:\\01-DEV-CODE-APPS\\01-Mes-APPS\\11-TimeTrackerV4\\netlify\\functions"
functionsDirectoryOrigin = "ui"
headersOrigin = "config"
redirectsOrigin = "config"
plugins = []

[forms]

[forms.settings]
error_template = "form-errors.html"

[functions]

[functions."*"]

[build]
publish = "F:\\01-DEV-CODE-APPS\\01-Mes-APPS\\11-TimeTrackerV4\\dist"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build"
functions = "F:\\01-DEV-CODE-APPS\\01-Mes-APPS\\11-TimeTrackerV4\\netlify\\functions"

[build.environment]
NODE_VERSION = "18"

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
for = "/assets/*"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]