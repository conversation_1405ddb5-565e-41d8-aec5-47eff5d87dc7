{"name": "pointeuse-d-activite-pro", "private": true, "version": "1.0.0", "type": "module", "description": "Application de pointeuse d'activité pour freelances et intérimaires", "keywords": ["timetracker", "freelance", "firebase", "react", "typescript"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "build:prod": "npm run type-check && vite build"}, "dependencies": {"firebase": "^12.0.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "terser": "^5.43.1", "typescript": "~5.7.2", "vite": "^6.2.0"}}